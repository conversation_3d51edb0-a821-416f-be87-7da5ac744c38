<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="#BA3830"
    app:popupTheme="@style/ThemeOverlay.MaterialComponents.Light"
    app:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/toolbar_profile_image"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:padding="2dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            android:foreground="@drawable/profile_image_ripple"
            android:clickable="true"
            android:focusable="true"
            android:visibility="visible"
            android:elevation="4dp"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            app:strokeColor="@android:color/white"
            app:strokeWidth="1dp" />

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:layout_toEndOf="@id/toolbar_profile_image"
            android:text="@string/app_name"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
            android:textColor="@android:color/white" />

        <FrameLayout
            android:id="@+id/toolbar_notification_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless">

            <include
                android:id="@+id/toolbar_notification_badge"
                layout="@layout/layout_notification_badge" />
        </FrameLayout>

    </RelativeLayout>

</androidx.appcompat.widget.Toolbar>
