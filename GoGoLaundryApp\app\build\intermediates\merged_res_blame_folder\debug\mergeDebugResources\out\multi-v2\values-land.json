{"logs": [{"outputFile": "com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-land\\dimens.xml", "from": {"startLines": "26,25,27,12,22,3,6,5,4,19,18,20,21,9,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1031,970,1091,452,867,111,248,203,160,703,647,759,818,352,538", "endColumns": "59,60,59,50,42,48,43,44,42,55,55,58,48,49,51", "endOffsets": "1086,1026,1146,498,905,155,287,243,198,754,698,813,862,397,585"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,32,33,34,35,36,39", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,324,385,445,496,539,588,632,677,2027,2083,2139,2198,2247,2429", "endColumns": "59,60,59,50,42,48,43,44,42,55,55,58,48,49,51", "endOffsets": "319,380,440,491,534,583,627,672,715,2078,2134,2193,2242,2292,2476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,35,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,277,347,419,489,554,621,691,763,832,901,983,1073,1149,1217,1284,1362,1427,1494,1666,2235,2504", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,34,39,42", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "125,199,272,342,414,484,549,616,686,758,827,896,978,1068,1144,1212,1279,1357,1422,1489,1661,2230,2499,2727"}, "to": {"startLines": "14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,37,38,40,44,53,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "720,795,869,942,1012,1084,1154,1219,1286,1356,1428,1497,1566,1648,1738,1814,1882,1949,2297,2362,2481,2653,3222,3491", "endLines": "14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,37,38,43,52,57,60", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "790,864,937,1007,1079,1149,1214,1281,1351,1423,1492,1561,1643,1733,1809,1877,1944,2022,2357,2424,2648,3217,3486,3714"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:\\values-land\\values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-land\\dimens.xml", "from": {"startLines": "26,25,27,12,22,3,6,5,4,19,18,20,21,9,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1031,970,1091,452,867,111,248,203,160,703,647,759,818,352,538", "endColumns": "59,60,59,50,42,48,43,44,42,55,55,58,48,49,51", "endOffsets": "1086,1026,1146,498,905,155,287,243,198,754,698,813,862,397,585"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,32,33,34,35,36,39", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,324,385,445,496,539,588,632,677,2027,2083,2139,2198,2247,2429", "endColumns": "59,60,59,50,42,48,43,44,42,55,55,58,48,49,51", "endOffsets": "319,380,440,491,534,583,627,672,715,2078,2134,2193,2242,2292,2476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,35,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,277,347,419,489,554,621,691,763,832,901,983,1073,1149,1217,1284,1362,1427,1494,1666,2235,2504", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,34,39,42", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "125,199,272,342,414,484,549,616,686,758,827,896,978,1068,1144,1212,1279,1357,1422,1489,1661,2230,2499,2727"}, "to": {"startLines": "14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,37,38,40,44,53,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "720,795,869,942,1012,1084,1154,1219,1286,1356,1428,1497,1566,1648,1738,1814,1882,1949,2297,2362,2481,2653,3222,3491", "endLines": "14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,37,38,43,52,57,60", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "790,864,937,1007,1079,1149,1214,1281,1351,1423,1492,1561,1643,1733,1809,1877,1944,2022,2357,2424,2648,3217,3486,3714"}}]}]}