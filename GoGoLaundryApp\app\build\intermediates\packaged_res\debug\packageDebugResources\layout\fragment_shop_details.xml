<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <!-- Enhanced App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="360dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="@color/colorPrimary"
            app:statusBarScrim="@color/colorPrimary"
            app:expandedTitleTextAppearance="@style/TextAppearance.App.CollapsingToolbar.Expanded"
            app:collapsedTitleTextAppearance="@style/TextAppearance.App.CollapsingToolbar.Collapsed"
            app:expandedTitleMarginStart="24dp"
            app:expandedTitleMarginBottom="100dp">

            <!-- Enhanced Hero Shop Cover Image -->
            <ImageView
                android:id="@+id/shopCoverImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/colorPrimary"
                app:layout_collapseMode="parallax"
                app:layout_collapseParallaxMultiplier="0.7"
                tools:src="@drawable/shop_cover_placeholder" />

            <!-- Enhanced Gradient Overlay -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/enhanced_gradient_overlay" />

            <!-- Action Icons Container (moved to top-right of cover image) -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_margin="16dp">

                <!-- Share Icon -->
                <ImageView
                    android:id="@+id/shareIcon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_share"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp"
                    android:layout_marginEnd="4dp"
                    app:tint="@color/white" />

                <!-- Favorite Icon -->
                <ImageView
                    android:id="@+id/favoriteIcon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_favorite_border"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp"
                    android:layout_marginEnd="4dp"
                    app:tint="@color/white" />

                <!-- Notification Icon -->
                <ImageView
                    android:id="@+id/notificationIcon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_notification"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp"
                    app:tint="@color/white" />

            </LinearLayout>

            <!-- Enhanced Shop Title Overlay -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:orientation="vertical"
                android:padding="24dp"
                android:layout_marginBottom="20dp">

                <!-- Shop Name with Enhanced Typography -->
                <TextView
                    android:id="@+id/shopNameTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/kalpurush"
                    android:shadowColor="@color/black"
                    android:shadowDx="0"
                    android:shadowDy="3"
                    android:shadowRadius="12"
                    android:layout_marginBottom="8dp"
                    android:maxLines="2"
                    android:ellipsize="end"
                    tools:text="Premium Laundry Service" />

                <!-- Shop Status and Distance Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- Live Status Badge -->
                    <TextView
                        android:id="@+id/liveStatusBadge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/live_status_badge"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp"
                        android:text="@string/open_now"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:layout_marginEnd="12dp"
                        tools:text="Open Now" />

                    <!-- Distance Badge -->
                    <TextView
                        android:id="@+id/distanceBadge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/distance_badge"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:drawableStart="@drawable/ic_location_small"
                        android:drawablePadding="4dp"
                        android:gravity="center_vertical"
                        tools:text="2.5 km away" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Enhanced Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/background_light"
            android:paddingBottom="100dp">

            <!-- Enhanced Shop Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/white"
                app:cardElevation="12dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Enhanced Address Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="20dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_location"
                            app:tint="@color/colorPrimary"
                            android:layout_marginEnd="12dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/shopAddressTextView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:lineSpacingExtra="3dp"
                                android:fontFamily="@font/kalpurush"
                                tools:text="R313, Sreepur, Sreepur Upazila (Gazipur), Gazipur District, Dhaka Division, Bangladesh" />

                            <!-- Distance and Delivery Info -->
                            <TextView
                                android:id="@+id/deliveryInfoTextView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:drawableStart="@drawable/ic_delivery"
                                android:drawablePadding="6dp"
                                android:gravity="center_vertical"
                                tools:text="Free delivery • 2.5 km away" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Enhanced Rating and Reviews Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="24dp"
                        android:background="@drawable/rating_background"
                        android:padding="16dp">

                        <!-- Rating Stars Container -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center">

                            <LinearLayout
                                android:id="@+id/ratingStarsLayout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="4dp">
                                <!-- Stars will be added programmatically -->
                            </LinearLayout>

                            <TextView
                                android:id="@+id/ratingTextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/kalpurush"
                                tools:text="4.5" />

                        </LinearLayout>

                        <!-- Reviews Info -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_marginStart="20dp">

                            <TextView
                                android:id="@+id/reviewsCountTextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                tools:text="Based on 127 reviews" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="2dp"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:text="Excellent service quality" />

                        </LinearLayout>

                        <!-- View Reviews Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/viewReviewsButton"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:text="View All"
                            android:textColor="@color/colorPrimary"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            app:cornerRadius="18dp" />

                    </LinearLayout>

                    <!-- Enhanced Action Buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <!-- Call Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/callButton"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="52dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="@string/call"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:icon="@drawable/ic_phone"
                            app:iconTint="@color/colorPrimary"
                            app:iconSize="20dp"
                            app:strokeColor="@color/colorPrimary"
                            app:strokeWidth="2dp"
                            app:cornerRadius="26dp" />

                        <!-- Directions Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/directionsButton"
                            style="@style/Widget.Material3.Button"
                            android:layout_width="0dp"
                            android:layout_height="52dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="@string/directions"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:icon="@drawable/ic_directions"
                            app:iconTint="@color/white"
                            app:iconSize="20dp"
                            app:backgroundTint="@color/success"
                            app:cornerRadius="26dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Enhanced Operating Hours Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/white"
                app:cardElevation="12dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Enhanced Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="20dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/service_card_shine_overlay"
                            app:tint="@color/colorPrimary"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/operating_hours"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/kalpurush" />

                        <!-- Current Status Indicator -->
                        <TextView
                            android:id="@+id/currentStatusIndicator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/status_indicator_background"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="6dp"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            tools:text="Open Now" />

                    </LinearLayout>

                    <!-- Dynamic Operating Hours Container -->
                    <LinearLayout
                        android:id="@+id/operatingHoursContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Hours will be populated dynamically from API -->
                        <!-- Default fallback hours shown below -->

                        <!-- Monday - Friday -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp"
                            android:background="@drawable/day_row_background">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Monday - Friday"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/kalpurush" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="9:00 AM - 6:00 PM"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp"
                                android:fontFamily="@font/kalpurush" />

                        </LinearLayout>

                        <!-- Saturday -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp"
                            android:background="@drawable/day_row_background">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Saturday"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/kalpurush" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="10:00 AM - 4:00 PM"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp"
                                android:fontFamily="@font/kalpurush" />

                        </LinearLayout>

                        <!-- Sunday -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp"
                            android:background="@drawable/day_row_background">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Sunday"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/kalpurush" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Closed"
                                android:textColor="@color/error"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/kalpurush" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Enhanced Services Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/white"
                app:cardElevation="12dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Enhanced Services Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="20dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_service"
                            app:tint="@color/colorPrimary"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Available Services"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/kalpurush" />

                        <!-- Service Count Badge -->
                        <TextView
                            android:id="@+id/serviceCountBadge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/count_badge_background"
                            android:paddingHorizontal="10dp"
                            android:paddingVertical="4dp"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            tools:text="5" />

                    </LinearLayout>

                    <!-- Enhanced Services RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/detailsRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        android:clipToPadding="false"
                        android:layout_marginBottom="16dp" />

                    <!-- View All Services Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/viewAllServicesButton"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="View All Services"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:icon="@drawable/ic_arrow_forward"
                        app:iconTint="@color/colorPrimary"
                        app:iconSize="18dp"
                        app:iconGravity="end"
                        app:cornerRadius="24dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Enhanced Quick Actions Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/white"
                app:cardElevation="12dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Quick Actions Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="20dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/warning_circle"
                            app:tint="@color/colorPrimary"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Quick Actions"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/kalpurush" />

                    </LinearLayout>

                    <!-- Quick Action Buttons Grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <!-- Express Service -->
                        <LinearLayout
                            android:id="@+id/expressServiceAction"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:layout_marginEnd="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/express"
                                app:tint="@color/colorPrimary"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Express"
                                android:textColor="@color/text_primary"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Schedule Pickup -->
                        <LinearLayout
                            android:id="@+id/schedulePickupAction"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:layout_marginHorizontal="4dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/accept"
                                app:tint="@color/colorPrimary"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Schedule"
                                android:textColor="@color/text_primary"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Track Order -->
                        <LinearLayout
                            android:id="@+id/trackOrderAction"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:layout_marginStart="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_time_modern"
                                app:tint="@color/colorPrimary"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Track"
                                android:textColor="@color/text_primary"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Enhanced Place Order Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/orderFab"
        style="@style/Widget.Material3.Button"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        android:text="Place Order Now"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:fontFamily="@font/kalpurush"
        app:icon="@drawable/ic_shopping_cart"
        app:iconTint="@color/white"
        app:iconSize="24dp"
        app:backgroundTint="@color/colorPrimary"
        app:cornerRadius="32dp"
        android:elevation="8dp"
        app:rippleColor="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
