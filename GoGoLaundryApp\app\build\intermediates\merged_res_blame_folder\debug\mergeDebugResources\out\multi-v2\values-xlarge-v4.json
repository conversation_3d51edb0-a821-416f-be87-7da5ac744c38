{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:\\values-xlarge-v4\\values-xlarge-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-xlarge\\dimens.xml", "from": {"startLines": "24,22,21,23,27,7,3,8,6,5,4,15,18,16,14,13,11,12,17,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,904,859,956,1091,287,105,337,243,198,154,612,758,665,565,517,423,470,711,1177", "endColumns": "42,51,44,49,50,49,48,42,43,44,43,52,49,45,46,47,46,46,46,51", "endOffsets": "1044,951,899,1001,1137,332,149,375,282,238,193,660,803,706,607,560,465,512,753,1224"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "473,516,568,613,663,714,764,813,856,900,945,989,1042,1092,1138,1185,1233,1280,1327,1374", "endColumns": "42,51,44,49,50,49,48,42,43,44,43,52,49,45,46,47,46,46,46,51", "endOffsets": "511,563,608,658,709,759,808,851,895,940,984,1037,1087,1133,1180,1228,1275,1322,1369,1421"}}]}, {"outputFile": "com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:/values-xlarge-v4/values-xlarge-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-xlarge\\dimens.xml", "from": {"startLines": "24,22,21,23,27,7,3,8,6,5,4,15,18,16,14,13,11,12,17,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,904,859,956,1091,287,105,337,243,198,154,612,758,665,565,517,423,470,711,1177", "endColumns": "42,51,44,49,50,49,48,42,43,44,43,52,49,45,46,47,46,46,46,51", "endOffsets": "1044,951,899,1001,1137,332,149,375,282,238,193,660,803,706,607,560,465,512,753,1224"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "473,516,568,613,663,714,764,813,856,900,945,989,1042,1092,1138,1185,1233,1280,1327,1374", "endColumns": "42,51,44,49,50,49,48,42,43,44,43,52,49,45,46,47,46,46,46,51", "endOffsets": "511,563,608,658,709,759,808,851,895,940,984,1037,1087,1133,1180,1228,1275,1322,1369,1421"}}]}]}