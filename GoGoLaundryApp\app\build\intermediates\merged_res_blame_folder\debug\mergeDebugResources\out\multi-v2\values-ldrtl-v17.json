{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:\\values-ldrtl-v17\\values-ldrtl-v17.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,191", "endColumns": "135,140", "endOffsets": "186,327"}}]}, {"outputFile": "com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:/values-ldrtl-v17/values-ldrtl-v17.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,191", "endColumns": "135,140", "endOffsets": "186,327"}}]}]}