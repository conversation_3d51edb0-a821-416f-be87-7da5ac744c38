package com.mdsadrulhasan.gogolaundry.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.mdsadrulhasan.gogolaundry.database.dao.ItemDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ItemDao_Impl;
import com.mdsadrulhasan.gogolaundry.database.dao.LaundryShopDao;
import com.mdsadrulhasan.gogolaundry.database.dao.LaundryShopDao_Impl;
import com.mdsadrulhasan.gogolaundry.database.dao.OrderDao;
import com.mdsadrulhasan.gogolaundry.database.dao.OrderDao_Impl;
import com.mdsadrulhasan.gogolaundry.database.dao.ServiceDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ServiceDao_Impl;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopItemDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopItemDao_Impl;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopServiceDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopServiceDao_Impl;
import com.mdsadrulhasan.gogolaundry.database.dao.UserDao;
import com.mdsadrulhasan.gogolaundry.database.dao.UserDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile UserDao _userDao;

  private volatile ServiceDao _serviceDao;

  private volatile OrderDao _orderDao;

  private volatile ItemDao _itemDao;

  private volatile LaundryShopDao _laundryShopDao;

  private volatile ShopServiceDao _shopServiceDao;

  private volatile ShopItemDao _shopItemDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(9) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER NOT NULL, `fullName` TEXT, `phone` TEXT, `email` TEXT, `address` TEXT, `divisionId` INTEGER, `districtId` INTEGER, `upazillaId` INTEGER, `isVerified` INTEGER NOT NULL, `profilePictureUrl` TEXT, `createdAt` INTEGER, `updatedAt` INTEGER, `divisionName` TEXT, `districtName` TEXT, `upazillaName` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `services` (`id` INTEGER NOT NULL, `name` TEXT, `bn_name` TEXT, `description` TEXT, `bn_description` TEXT, `image_url` TEXT, `is_active` INTEGER NOT NULL, `sort_order` INTEGER NOT NULL, `created_at` INTEGER, `updated_at` INTEGER, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `orders` (`id` INTEGER NOT NULL, `order_number` TEXT, `tracking_number` TEXT, `user_id` INTEGER NOT NULL, `delivery_personnel_id` INTEGER, `promo_code_id` INTEGER, `subtotal` REAL NOT NULL, `discount` REAL NOT NULL, `delivery_fee` REAL NOT NULL, `total` REAL NOT NULL, `payment_method` TEXT, `payment_status` TEXT, `status` TEXT, `pickup_address` TEXT, `pickup_division_id` INTEGER, `pickup_district_id` INTEGER, `pickup_upazilla_id` INTEGER, `pickup_date` INTEGER, `pickup_time_slot` TEXT, `delivery_address` TEXT, `delivery_division_id` INTEGER, `delivery_district_id` INTEGER, `delivery_upazilla_id` INTEGER, `delivery_date` INTEGER, `delivery_time_slot` TEXT, `notes` TEXT, `created_at` INTEGER, `updated_at` INTEGER, `customerName` TEXT, `customerPhone` TEXT, `deliveryPersonName` TEXT, `deliveryPersonPhone` TEXT, `promoCode` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `order_items` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `order_id` INTEGER NOT NULL, `item_id` INTEGER NOT NULL, `quantity` INTEGER NOT NULL, `price` REAL NOT NULL, `subtotal` REAL NOT NULL, `created_at` INTEGER, `updated_at` INTEGER, `itemName` TEXT, `itemBnName` TEXT, `itemImageUrl` TEXT, `serviceName` TEXT, `serviceBnName` TEXT, FOREIGN KEY(`order_id`) REFERENCES `orders`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_order_items_order_id` ON `order_items` (`order_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `items` (`id` INTEGER NOT NULL, `service_id` INTEGER NOT NULL, `name` TEXT, `bn_name` TEXT, `description` TEXT, `bn_description` TEXT, `price` REAL NOT NULL, `image_url` TEXT, `is_active` INTEGER NOT NULL, `in_stock` INTEGER NOT NULL, `serviceName` TEXT, `created_at` INTEGER, `updated_at` INTEGER, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `laundry_shops` (`id` INTEGER NOT NULL, `name` TEXT, `bn_name` TEXT, `description` TEXT, `bn_description` TEXT, `owner_name` TEXT, `phone` TEXT, `email` TEXT, `address` TEXT, `division_id` INTEGER, `district_id` INTEGER, `upazilla_id` INTEGER, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `operating_hours` TEXT, `rating` REAL NOT NULL, `total_reviews` INTEGER NOT NULL, `commission_percentage` REAL NOT NULL, `is_active` INTEGER NOT NULL, `is_verified` INTEGER NOT NULL, `profile_image_url` TEXT, `cover_image_url` TEXT, `created_at` INTEGER, `updated_at` INTEGER, `divisionName` TEXT, `districtName` TEXT, `upazillaName` TEXT, `distance` REAL NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `shop_services` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `shop_id` INTEGER NOT NULL, `service_id` INTEGER NOT NULL, `is_available` INTEGER NOT NULL, `estimated_hours` INTEGER NOT NULL, `created_at` INTEGER, `serviceName` TEXT, `serviceBnName` TEXT, `serviceImageUrl` TEXT, `description` TEXT, `bn_description` TEXT, `base_price` REAL NOT NULL, FOREIGN KEY(`shop_id`) REFERENCES `laundry_shops`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`service_id`) REFERENCES `services`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_shop_services_shop_id_service_id` ON `shop_services` (`shop_id`, `service_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_shop_services_shop_id` ON `shop_services` (`shop_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_shop_services_service_id` ON `shop_services` (`service_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `shop_items` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `shop_id` INTEGER NOT NULL, `item_id` INTEGER NOT NULL, `custom_price` REAL, `is_available` INTEGER NOT NULL, `estimated_hours` INTEGER NOT NULL, `created_at` INTEGER, `itemName` TEXT, `itemBnName` TEXT, `itemImageUrl` TEXT, `description` TEXT, `bn_description` TEXT, `defaultPrice` REAL NOT NULL, `serviceName` TEXT, `serviceBnName` TEXT, FOREIGN KEY(`shop_id`) REFERENCES `laundry_shops`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`item_id`) REFERENCES `items`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_shop_items_shop_id_item_id` ON `shop_items` (`shop_id`, `item_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_shop_items_shop_id` ON `shop_items` (`shop_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_shop_items_item_id` ON `shop_items` (`item_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'dfe30dcbcdac7b1d047ad071f9fa3174')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `users`");
        db.execSQL("DROP TABLE IF EXISTS `services`");
        db.execSQL("DROP TABLE IF EXISTS `orders`");
        db.execSQL("DROP TABLE IF EXISTS `order_items`");
        db.execSQL("DROP TABLE IF EXISTS `items`");
        db.execSQL("DROP TABLE IF EXISTS `laundry_shops`");
        db.execSQL("DROP TABLE IF EXISTS `shop_services`");
        db.execSQL("DROP TABLE IF EXISTS `shop_items`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsUsers = new HashMap<String, TableInfo.Column>(15);
        _columnsUsers.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("fullName", new TableInfo.Column("fullName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("phone", new TableInfo.Column("phone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("address", new TableInfo.Column("address", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("divisionId", new TableInfo.Column("divisionId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("districtId", new TableInfo.Column("districtId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("upazillaId", new TableInfo.Column("upazillaId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("isVerified", new TableInfo.Column("isVerified", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("profilePictureUrl", new TableInfo.Column("profilePictureUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("divisionName", new TableInfo.Column("divisionName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("districtName", new TableInfo.Column("districtName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("upazillaName", new TableInfo.Column("upazillaName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUsers = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUsers = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUsers = new TableInfo("users", _columnsUsers, _foreignKeysUsers, _indicesUsers);
        final TableInfo _existingUsers = TableInfo.read(db, "users");
        if (!_infoUsers.equals(_existingUsers)) {
          return new RoomOpenHelper.ValidationResult(false, "users(com.mdsadrulhasan.gogolaundry.database.entity.UserEntity).\n"
                  + " Expected:\n" + _infoUsers + "\n"
                  + " Found:\n" + _existingUsers);
        }
        final HashMap<String, TableInfo.Column> _columnsServices = new HashMap<String, TableInfo.Column>(10);
        _columnsServices.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("name", new TableInfo.Column("name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("bn_name", new TableInfo.Column("bn_name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("bn_description", new TableInfo.Column("bn_description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("image_url", new TableInfo.Column("image_url", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("sort_order", new TableInfo.Column("sort_order", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsServices.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysServices = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesServices = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoServices = new TableInfo("services", _columnsServices, _foreignKeysServices, _indicesServices);
        final TableInfo _existingServices = TableInfo.read(db, "services");
        if (!_infoServices.equals(_existingServices)) {
          return new RoomOpenHelper.ValidationResult(false, "services(com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity).\n"
                  + " Expected:\n" + _infoServices + "\n"
                  + " Found:\n" + _existingServices);
        }
        final HashMap<String, TableInfo.Column> _columnsOrders = new HashMap<String, TableInfo.Column>(33);
        _columnsOrders.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("order_number", new TableInfo.Column("order_number", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("tracking_number", new TableInfo.Column("tracking_number", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("user_id", new TableInfo.Column("user_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_personnel_id", new TableInfo.Column("delivery_personnel_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("promo_code_id", new TableInfo.Column("promo_code_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("subtotal", new TableInfo.Column("subtotal", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("discount", new TableInfo.Column("discount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_fee", new TableInfo.Column("delivery_fee", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("total", new TableInfo.Column("total", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("payment_method", new TableInfo.Column("payment_method", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("payment_status", new TableInfo.Column("payment_status", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("status", new TableInfo.Column("status", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("pickup_address", new TableInfo.Column("pickup_address", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("pickup_division_id", new TableInfo.Column("pickup_division_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("pickup_district_id", new TableInfo.Column("pickup_district_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("pickup_upazilla_id", new TableInfo.Column("pickup_upazilla_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("pickup_date", new TableInfo.Column("pickup_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("pickup_time_slot", new TableInfo.Column("pickup_time_slot", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_address", new TableInfo.Column("delivery_address", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_division_id", new TableInfo.Column("delivery_division_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_district_id", new TableInfo.Column("delivery_district_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_upazilla_id", new TableInfo.Column("delivery_upazilla_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_date", new TableInfo.Column("delivery_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("delivery_time_slot", new TableInfo.Column("delivery_time_slot", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("customerName", new TableInfo.Column("customerName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("customerPhone", new TableInfo.Column("customerPhone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("deliveryPersonName", new TableInfo.Column("deliveryPersonName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("deliveryPersonPhone", new TableInfo.Column("deliveryPersonPhone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrders.put("promoCode", new TableInfo.Column("promoCode", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOrders = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesOrders = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoOrders = new TableInfo("orders", _columnsOrders, _foreignKeysOrders, _indicesOrders);
        final TableInfo _existingOrders = TableInfo.read(db, "orders");
        if (!_infoOrders.equals(_existingOrders)) {
          return new RoomOpenHelper.ValidationResult(false, "orders(com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity).\n"
                  + " Expected:\n" + _infoOrders + "\n"
                  + " Found:\n" + _existingOrders);
        }
        final HashMap<String, TableInfo.Column> _columnsOrderItems = new HashMap<String, TableInfo.Column>(13);
        _columnsOrderItems.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("order_id", new TableInfo.Column("order_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("item_id", new TableInfo.Column("item_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("quantity", new TableInfo.Column("quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("price", new TableInfo.Column("price", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("subtotal", new TableInfo.Column("subtotal", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("itemName", new TableInfo.Column("itemName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("itemBnName", new TableInfo.Column("itemBnName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("itemImageUrl", new TableInfo.Column("itemImageUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("serviceName", new TableInfo.Column("serviceName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrderItems.put("serviceBnName", new TableInfo.Column("serviceBnName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOrderItems = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysOrderItems.add(new TableInfo.ForeignKey("orders", "CASCADE", "NO ACTION", Arrays.asList("order_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesOrderItems = new HashSet<TableInfo.Index>(1);
        _indicesOrderItems.add(new TableInfo.Index("index_order_items_order_id", false, Arrays.asList("order_id"), Arrays.asList("ASC")));
        final TableInfo _infoOrderItems = new TableInfo("order_items", _columnsOrderItems, _foreignKeysOrderItems, _indicesOrderItems);
        final TableInfo _existingOrderItems = TableInfo.read(db, "order_items");
        if (!_infoOrderItems.equals(_existingOrderItems)) {
          return new RoomOpenHelper.ValidationResult(false, "order_items(com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity).\n"
                  + " Expected:\n" + _infoOrderItems + "\n"
                  + " Found:\n" + _existingOrderItems);
        }
        final HashMap<String, TableInfo.Column> _columnsItems = new HashMap<String, TableInfo.Column>(13);
        _columnsItems.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("service_id", new TableInfo.Column("service_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("name", new TableInfo.Column("name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("bn_name", new TableInfo.Column("bn_name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("bn_description", new TableInfo.Column("bn_description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("price", new TableInfo.Column("price", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("image_url", new TableInfo.Column("image_url", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("in_stock", new TableInfo.Column("in_stock", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("serviceName", new TableInfo.Column("serviceName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsItems.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysItems = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesItems = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoItems = new TableInfo("items", _columnsItems, _foreignKeysItems, _indicesItems);
        final TableInfo _existingItems = TableInfo.read(db, "items");
        if (!_infoItems.equals(_existingItems)) {
          return new RoomOpenHelper.ValidationResult(false, "items(com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity).\n"
                  + " Expected:\n" + _infoItems + "\n"
                  + " Found:\n" + _existingItems);
        }
        final HashMap<String, TableInfo.Column> _columnsLaundryShops = new HashMap<String, TableInfo.Column>(28);
        _columnsLaundryShops.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("name", new TableInfo.Column("name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("bn_name", new TableInfo.Column("bn_name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("bn_description", new TableInfo.Column("bn_description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("owner_name", new TableInfo.Column("owner_name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("phone", new TableInfo.Column("phone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("address", new TableInfo.Column("address", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("division_id", new TableInfo.Column("division_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("district_id", new TableInfo.Column("district_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("upazilla_id", new TableInfo.Column("upazilla_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("latitude", new TableInfo.Column("latitude", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("longitude", new TableInfo.Column("longitude", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("operating_hours", new TableInfo.Column("operating_hours", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("rating", new TableInfo.Column("rating", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("total_reviews", new TableInfo.Column("total_reviews", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("commission_percentage", new TableInfo.Column("commission_percentage", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("is_verified", new TableInfo.Column("is_verified", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("profile_image_url", new TableInfo.Column("profile_image_url", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("cover_image_url", new TableInfo.Column("cover_image_url", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("divisionName", new TableInfo.Column("divisionName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("districtName", new TableInfo.Column("districtName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("upazillaName", new TableInfo.Column("upazillaName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLaundryShops.put("distance", new TableInfo.Column("distance", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysLaundryShops = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesLaundryShops = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoLaundryShops = new TableInfo("laundry_shops", _columnsLaundryShops, _foreignKeysLaundryShops, _indicesLaundryShops);
        final TableInfo _existingLaundryShops = TableInfo.read(db, "laundry_shops");
        if (!_infoLaundryShops.equals(_existingLaundryShops)) {
          return new RoomOpenHelper.ValidationResult(false, "laundry_shops(com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity).\n"
                  + " Expected:\n" + _infoLaundryShops + "\n"
                  + " Found:\n" + _existingLaundryShops);
        }
        final HashMap<String, TableInfo.Column> _columnsShopServices = new HashMap<String, TableInfo.Column>(12);
        _columnsShopServices.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("shop_id", new TableInfo.Column("shop_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("service_id", new TableInfo.Column("service_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("is_available", new TableInfo.Column("is_available", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("estimated_hours", new TableInfo.Column("estimated_hours", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("serviceName", new TableInfo.Column("serviceName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("serviceBnName", new TableInfo.Column("serviceBnName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("serviceImageUrl", new TableInfo.Column("serviceImageUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("bn_description", new TableInfo.Column("bn_description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopServices.put("base_price", new TableInfo.Column("base_price", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysShopServices = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysShopServices.add(new TableInfo.ForeignKey("laundry_shops", "CASCADE", "NO ACTION", Arrays.asList("shop_id"), Arrays.asList("id")));
        _foreignKeysShopServices.add(new TableInfo.ForeignKey("services", "CASCADE", "NO ACTION", Arrays.asList("service_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesShopServices = new HashSet<TableInfo.Index>(3);
        _indicesShopServices.add(new TableInfo.Index("index_shop_services_shop_id_service_id", true, Arrays.asList("shop_id", "service_id"), Arrays.asList("ASC", "ASC")));
        _indicesShopServices.add(new TableInfo.Index("index_shop_services_shop_id", false, Arrays.asList("shop_id"), Arrays.asList("ASC")));
        _indicesShopServices.add(new TableInfo.Index("index_shop_services_service_id", false, Arrays.asList("service_id"), Arrays.asList("ASC")));
        final TableInfo _infoShopServices = new TableInfo("shop_services", _columnsShopServices, _foreignKeysShopServices, _indicesShopServices);
        final TableInfo _existingShopServices = TableInfo.read(db, "shop_services");
        if (!_infoShopServices.equals(_existingShopServices)) {
          return new RoomOpenHelper.ValidationResult(false, "shop_services(com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity).\n"
                  + " Expected:\n" + _infoShopServices + "\n"
                  + " Found:\n" + _existingShopServices);
        }
        final HashMap<String, TableInfo.Column> _columnsShopItems = new HashMap<String, TableInfo.Column>(15);
        _columnsShopItems.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("shop_id", new TableInfo.Column("shop_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("item_id", new TableInfo.Column("item_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("custom_price", new TableInfo.Column("custom_price", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("is_available", new TableInfo.Column("is_available", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("estimated_hours", new TableInfo.Column("estimated_hours", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("created_at", new TableInfo.Column("created_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("itemName", new TableInfo.Column("itemName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("itemBnName", new TableInfo.Column("itemBnName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("itemImageUrl", new TableInfo.Column("itemImageUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("bn_description", new TableInfo.Column("bn_description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("defaultPrice", new TableInfo.Column("defaultPrice", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("serviceName", new TableInfo.Column("serviceName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsShopItems.put("serviceBnName", new TableInfo.Column("serviceBnName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysShopItems = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysShopItems.add(new TableInfo.ForeignKey("laundry_shops", "CASCADE", "NO ACTION", Arrays.asList("shop_id"), Arrays.asList("id")));
        _foreignKeysShopItems.add(new TableInfo.ForeignKey("items", "CASCADE", "NO ACTION", Arrays.asList("item_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesShopItems = new HashSet<TableInfo.Index>(3);
        _indicesShopItems.add(new TableInfo.Index("index_shop_items_shop_id_item_id", true, Arrays.asList("shop_id", "item_id"), Arrays.asList("ASC", "ASC")));
        _indicesShopItems.add(new TableInfo.Index("index_shop_items_shop_id", false, Arrays.asList("shop_id"), Arrays.asList("ASC")));
        _indicesShopItems.add(new TableInfo.Index("index_shop_items_item_id", false, Arrays.asList("item_id"), Arrays.asList("ASC")));
        final TableInfo _infoShopItems = new TableInfo("shop_items", _columnsShopItems, _foreignKeysShopItems, _indicesShopItems);
        final TableInfo _existingShopItems = TableInfo.read(db, "shop_items");
        if (!_infoShopItems.equals(_existingShopItems)) {
          return new RoomOpenHelper.ValidationResult(false, "shop_items(com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity).\n"
                  + " Expected:\n" + _infoShopItems + "\n"
                  + " Found:\n" + _existingShopItems);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "dfe30dcbcdac7b1d047ad071f9fa3174", "dd8caea987a57c3eaf9f9e9ad21d2846");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "users","services","orders","order_items","items","laundry_shops","shop_services","shop_items");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `users`");
      _db.execSQL("DELETE FROM `services`");
      _db.execSQL("DELETE FROM `orders`");
      _db.execSQL("DELETE FROM `order_items`");
      _db.execSQL("DELETE FROM `items`");
      _db.execSQL("DELETE FROM `laundry_shops`");
      _db.execSQL("DELETE FROM `shop_services`");
      _db.execSQL("DELETE FROM `shop_items`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(UserDao.class, UserDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ServiceDao.class, ServiceDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(OrderDao.class, OrderDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ItemDao.class, ItemDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(LaundryShopDao.class, LaundryShopDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ShopServiceDao.class, ShopServiceDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ShopItemDao.class, ShopItemDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public UserDao userDao() {
    if (_userDao != null) {
      return _userDao;
    } else {
      synchronized(this) {
        if(_userDao == null) {
          _userDao = new UserDao_Impl(this);
        }
        return _userDao;
      }
    }
  }

  @Override
  public ServiceDao serviceDao() {
    if (_serviceDao != null) {
      return _serviceDao;
    } else {
      synchronized(this) {
        if(_serviceDao == null) {
          _serviceDao = new ServiceDao_Impl(this);
        }
        return _serviceDao;
      }
    }
  }

  @Override
  public OrderDao orderDao() {
    if (_orderDao != null) {
      return _orderDao;
    } else {
      synchronized(this) {
        if(_orderDao == null) {
          _orderDao = new OrderDao_Impl(this);
        }
        return _orderDao;
      }
    }
  }

  @Override
  public ItemDao itemDao() {
    if (_itemDao != null) {
      return _itemDao;
    } else {
      synchronized(this) {
        if(_itemDao == null) {
          _itemDao = new ItemDao_Impl(this);
        }
        return _itemDao;
      }
    }
  }

  @Override
  public LaundryShopDao laundryShopDao() {
    if (_laundryShopDao != null) {
      return _laundryShopDao;
    } else {
      synchronized(this) {
        if(_laundryShopDao == null) {
          _laundryShopDao = new LaundryShopDao_Impl(this);
        }
        return _laundryShopDao;
      }
    }
  }

  @Override
  public ShopServiceDao shopServiceDao() {
    if (_shopServiceDao != null) {
      return _shopServiceDao;
    } else {
      synchronized(this) {
        if(_shopServiceDao == null) {
          _shopServiceDao = new ShopServiceDao_Impl(this);
        }
        return _shopServiceDao;
      }
    }
  }

  @Override
  public ShopItemDao shopItemDao() {
    if (_shopItemDao != null) {
      return _shopItemDao;
    } else {
      synchronized(this) {
        if(_shopItemDao == null) {
          _shopItemDao = new ShopItemDao_Impl(this);
        }
        return _shopItemDao;
      }
    }
  }
}
